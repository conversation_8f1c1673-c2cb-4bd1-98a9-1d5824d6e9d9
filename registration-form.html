<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Registration Form</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .registration-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            padding: 40px;
            width: 100%;
            max-width: 500px;
            position: relative;
            overflow: hidden;
        }

        .registration-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .form-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .form-title {
            color: #333;
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
        }

        .form-subtitle {
            color: #666;
            font-size: 1rem;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-label {
            display: block;
            color: #555;
            font-weight: 500;
            margin-bottom: 8px;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-input:valid {
            border-color: #28a745;
        }

        .form-input:invalid:not(:placeholder-shown) {
            border-color: #dc3545;
        }

        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 25px;
        }

        .checkbox-input {
            width: 18px;
            height: 18px;
            accent-color: #667eea;
        }

        .checkbox-label {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .checkbox-label a {
            color: #667eea;
            text-decoration: none;
        }

        .checkbox-label a:hover {
            text-decoration: underline;
        }

        .submit-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        .login-link {
            text-align: center;
            margin-top: 25px;
            color: #666;
            font-size: 0.9rem;
        }

        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .registration-container {
                padding: 30px 25px;
                margin: 10px;
            }

            .form-title {
                font-size: 2rem;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }

        @media (max-width: 480px) {
            .registration-container {
                padding: 25px 20px;
            }

            .form-title {
                font-size: 1.8rem;
            }

            .form-input {
                padding: 12px 15px;
            }
        }

        /* Form validation styles */
        .error-message {
            color: #dc3545;
            font-size: 0.8rem;
            margin-top: 5px;
            display: none;
        }

        .form-input:invalid:not(:placeholder-shown) + .error-message {
            display: block;
        }

        /* Success message */
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
            display: none;
        }
    </style>
</head>
<body>
    <div class="registration-container">
        <div class="form-header">
            <h1 class="form-title">Sign Up</h1>
            <p class="form-subtitle">Create your account to get started</p>
        </div>

        <div class="success-message" id="successMessage">
            <strong>Success!</strong> Your account has been created successfully.
        </div>

        <form id="registrationForm" novalidate>
            <div class="form-row">
                <div class="form-group">
                    <label for="firstName" class="form-label">First Name</label>
                    <input 
                        type="text" 
                        id="firstName" 
                        name="firstName" 
                        class="form-input" 
                        placeholder="Enter your first name"
                        required
                        minlength="2"
                    >
                    <div class="error-message">Please enter a valid first name (at least 2 characters)</div>
                </div>
                <div class="form-group">
                    <label for="lastName" class="form-label">Last Name</label>
                    <input 
                        type="text" 
                        id="lastName" 
                        name="lastName" 
                        class="form-input" 
                        placeholder="Enter your last name"
                        required
                        minlength="2"
                    >
                    <div class="error-message">Please enter a valid last name (at least 2 characters)</div>
                </div>
            </div>

            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    class="form-input" 
                    placeholder="Enter your email address"
                    required
                >
                <div class="error-message">Please enter a valid email address</div>
            </div>

            <div class="form-group">
                <label for="phone" class="form-label">Phone Number</label>
                <input 
                    type="tel" 
                    id="phone" 
                    name="phone" 
                    class="form-input" 
                    placeholder="Enter your phone number"
                    pattern="[0-9]{10,15}"
                    required
                >
                <div class="error-message">Please enter a valid phone number (10-15 digits)</div>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    class="form-input" 
                    placeholder="Create a strong password"
                    required
                    minlength="8"
                    pattern="(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}"
                >
                <div class="error-message">Password must be at least 8 characters with uppercase, lowercase, number, and special character</div>
            </div>

            <div class="form-group">
                <label for="confirmPassword" class="form-label">Confirm Password</label>
                <input 
                    type="password" 
                    id="confirmPassword" 
                    name="confirmPassword" 
                    class="form-input" 
                    placeholder="Confirm your password"
                    required
                >
                <div class="error-message">Passwords do not match</div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="dateOfBirth" class="form-label">Date of Birth</label>
                    <input 
                        type="date" 
                        id="dateOfBirth" 
                        name="dateOfBirth" 
                        class="form-input" 
                        required
                    >
                    <div class="error-message">Please enter your date of birth</div>
                </div>
                <div class="form-group">
                    <label for="gender" class="form-label">Gender</label>
                    <select id="gender" name="gender" class="form-input" required>
                        <option value="">Select Gender</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                        <option value="prefer-not-to-say">Prefer not to say</option>
                    </select>
                    <div class="error-message">Please select your gender</div>
                </div>
            </div>

            <div class="checkbox-group">
                <input type="checkbox" id="terms" name="terms" class="checkbox-input" required>
                <label for="terms" class="checkbox-label">
                    I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
                </label>
            </div>

            <div class="checkbox-group">
                <input type="checkbox" id="newsletter" name="newsletter" class="checkbox-input">
                <label for="newsletter" class="checkbox-label">
                    I would like to receive marketing emails and updates
                </label>
            </div>

            <button type="submit" class="submit-btn">Create Account</button>
        </form>

        <div class="login-link">
            Already have an account? <a href="#" id="loginLink">Sign In</a>
        </div>
    </div>

    <script>
        document.getElementById('registrationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const password = formData.get('password');
            const confirmPassword = formData.get('confirmPassword');
            
            // Check if passwords match
            if (password !== confirmPassword) {
                document.querySelector('#confirmPassword + .error-message').style.display = 'block';
                document.getElementById('confirmPassword').style.borderColor = '#dc3545';
                return;
            } else {
                document.querySelector('#confirmPassword + .error-message').style.display = 'none';
                document.getElementById('confirmPassword').style.borderColor = '#28a745';
            }
            
            // Check if form is valid
            if (this.checkValidity()) {
                // Show success message
                document.getElementById('successMessage').style.display = 'block';
                
                // Reset form
                this.reset();
                
                // Hide success message after 5 seconds
                setTimeout(() => {
                    document.getElementById('successMessage').style.display = 'none';
                }, 5000);
                
                console.log('Form submitted successfully!');
                console.log('Form data:', Object.fromEntries(formData));
            } else {
                // Show validation errors
                this.reportValidity();
            }
        });

        // Real-time password confirmation validation
        document.getElementById('confirmPassword').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && password !== confirmPassword) {
                this.style.borderColor = '#dc3545';
                document.querySelector('#confirmPassword + .error-message').style.display = 'block';
            } else if (confirmPassword && password === confirmPassword) {
                this.style.borderColor = '#28a745';
                document.querySelector('#confirmPassword + .error-message').style.display = 'none';
            }
        });

        // Age validation (must be at least 13 years old)
        document.getElementById('dateOfBirth').addEventListener('change', function() {
            const today = new Date();
            const birthDate = new Date(this.value);
            const age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }
            
            if (age < 13) {
                this.setCustomValidity('You must be at least 13 years old to register');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
